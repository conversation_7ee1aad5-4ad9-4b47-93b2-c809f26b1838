#########################################
# Memory-optimized version of McSearch batch processing
# Optimizations for memory management and performance
# Based on original script by <PERSON><PERSON> Xing
#########################################

# Parameter setting (same as original)
data.path <- getwd()
db.name <- '<PERSON>ehn HILIC_HNL library.msp'
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mass.range <- 200
mz.tol <- 0.01
topNo <- 100
HNL.threshold <- 36
ion.mode <- 'p'

# Memory management settings
options(warn = 1)  # Show warnings immediately
gc.interval <- 50  # Run garbage collection every 50 spectra

# Load libraries
library('readMzXmlData')
library('clue')
library('metaMS')
library("CHNOSZ")
library("ChemmineR") 
library('ChemmineOB')
library("fmcsR")
library('stringr')
library('dplyr')
library('Rdisop')

# Memory monitoring function
monitor_memory <- function(step_name) {
  mem_info <- gc()
  cat(sprintf("Memory after %s: Used %.1f MB, Max %.1f MB\n", 
              step_name, 
              sum(mem_info[,2]), 
              sum(mem_info[,6])))
}

# Optimized CSS score function with pre-allocation
CSS.score.optimized <- function(HNL.q, ms2.l){
  if(nrow(ms2.l) > topNo){
    ms2.l <- ms2.l[ms2.l[,2] > sort(ms2.l[,2], decreasing=TRUE)[topNo+1], ]
  }
  
  # Pre-allocate alignment matrix instead of growing it
  max_alignments <- min(nrow(HNL.q) * nrow(ms2.l), 10000)  # Cap maximum size
  HNL.alignment <- matrix(NA, nrow = max_alignments, ncol = 4)
  colnames(HNL.alignment) <- c('HNL.q','int.q','HNL.l','int.l')
  
  alignment_count <- 0
  for(m in 1:nrow(HNL.q)){
    mz.diff <- abs(HNL.q[m,1] - ms2.l[,1])
    matches <- which(mz.diff <= mz.tol)
    
    if(length(matches) > 0){
      for(match_idx in matches){
        alignment_count <- alignment_count + 1
        if(alignment_count <= max_alignments){
          HNL.alignment[alignment_count, ] <- c(HNL.q[m,1], HNL.q[m,6], 
                                               ms2.l[match_idx,1], ms2.l[match_idx,2])
        }
      }
    }
  }
  
  if(alignment_count == 0){
    return(list(0, 0, nrow(ms2.l)))
  }
  
  # Trim to actual size
  HNL.alignment <- HNL.alignment[1:alignment_count, , drop = FALSE]
  HNL.alignment <- HNL.alignment[complete.cases(HNL.alignment), , drop = FALSE]
  
  CSS <- 0
  mp <- 0
  
  if(nrow(HNL.alignment) > 0){
    uniqueHNL.q <- unique(HNL.alignment[,1])
    uniqueHNL.l <- unique(HNL.alignment[,3])
    max.length <- max(length(uniqueHNL.q), length(uniqueHNL.l))
    
    # Pre-allocate matrix
    matrix_size <- max.length + 1
    matrix <- matrix(0, nrow = matrix_size, ncol = matrix_size)
    
    # Fill matrix more efficiently
    matrix[2:nrow(matrix), 1] <- c(uniqueHNL.q, rep(0, max.length - length(uniqueHNL.q)))
    matrix[1, 2:ncol(matrix)] <- c(uniqueHNL.l, rep(0, max.length - length(uniqueHNL.l)))
    
    # Vectorized matrix filling
    for(m in 1:nrow(HNL.alignment)){
      row_idx <- which(matrix[,1] == HNL.alignment[m,1])
      col_idx <- which(matrix[1,] == HNL.alignment[m,3])
      if(length(row_idx) > 0 && length(col_idx) > 0){
        matrix[row_idx, col_idx] <- HNL.alignment[m,2] * HNL.alignment[m,4]
      }
    }
    
    matrix.B <- matrix[2:nrow(matrix), 2:ncol(matrix)]
    
    # Hungarian algorithm
    optimal <- solve_LSAP(matrix.B, maximum = TRUE)
    
    # Calculate CSS score
    sum.q <- 0
    for(m in 1:max.length){
      CSS <- CSS + matrix.B[m, optimal[m]]
      if(matrix.B[m, optimal[m]] > 0){
        matching_hnl <- matrix[m+1, 1]
        sum.q <- sum.q + max(HNL.q[HNL.q[,1] == matching_hnl, 6])^2
        mp <- mp + 1
      }
    }
    CSS <- CSS / (sum.q * sum(ms2.l[,2]^2))^0.5
  }
  
  return(list(CSS, mp, nrow(ms2.l)))
}

# Optimized HNL matrix generation
generate_HNL_matrix <- function(ms2.Q, HNL.threshold) {
  n_peaks <- nrow(ms2.Q)
  max_combinations <- n_peaks * (n_peaks - 1) / 2
  
  # Pre-allocate HNL matrix
  HNL.Q <- matrix(NA, nrow = max_combinations, ncol = 6)
  colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
  
  h <- 1
  for(m in 1:(n_peaks-1)){
    for(n in (m+1):n_peaks){
      hnl_value <- ms2.Q[n,1] - ms2.Q[m,1]
      if(hnl_value >= HNL.threshold){
        HNL.Q[h, ] <- c(hnl_value, ms2.Q[n,1], ms2.Q[m,1], 
                        ms2.Q[n,2], ms2.Q[m,2],
                        ifelse(m==1, ms2.Q[n,2], 0.5*(ms2.Q[n,2]+ms2.Q[m,2])))
        h <- h + 1
      }
    }
  }
  
  # Trim to actual size and convert to data frame
  HNL.Q <- as.data.frame(HNL.Q[1:(h-1), , drop = FALSE])
  return(HNL.Q)
}

# Process data in chunks to manage memory
process_spectra_chunk <- function(data_chunk, database, bad.No, start_idx) {
  results <- list()
  
  for(i in 1:length(data_chunk)){
    spectrum_idx <- start_idx + i - 1
    
    # Skip non-MS2 spectra
    if(data_chunk[[i]]$metaData$msLevel != 2) next
    if(length(data_chunk[[i]]$spectrum$mass) == 0) next
    
    cat(sprintf("Processing spectrum %d...\n", spectrum_idx))
    
    # Extract spectrum information
    premass.Q <- data_chunk[[i]]$metaData$precursorMz
    ms2.Q <- as.data.frame(cbind(data_chunk[[i]]$spectrum$mass, data_chunk[[i]]$spectrum$intensity))
    rt.Q <- data_chunk[[i]]$metaData$retentionTime
    
    # Calculate neutral mass
    if(ion.mode %in% c('P','p')){ 
      mass.Q <- premass.Q - 1.007276
      adduct <- '[M+H]+'
      ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
    } else {
      mass.Q <- premass.Q + 1.007276
      adduct <- '[M-H]-'
      ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
    }
    
    # Preprocess MS2 spectrum
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max(ms2.Q[,2])
    ms2.Q <- ms2.Q[ms2.Q[,2] >= 1, ]
    ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol), ]
    
    if(nrow(ms2.Q) > 30){
      ms2.Q <- ms2.Q[ms2.Q[,2] > sort(ms2.Q[,2], decreasing=TRUE)[31], ]
    }
    
    ms2.Q[,2] <- sqrt(ms2.Q[,2])
    
    if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol){
      ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    }
    
    # Generate HNL matrix efficiently
    HNL.Q <- generate_HNL_matrix(ms2.Q, HNL.threshold)
    
    if(nrow(HNL.Q) == 0) next
    
    # Process HNL peaks
    HNL.Q.1st <- HNL.Q[1:(nrow(ms2.Q)-1), ]
    HNL.Q.2nd <- HNL.Q[nrow(ms2.Q):nrow(HNL.Q), ]
    
    if(nrow(HNL.Q.2nd) > topNo){
      HNL.Q.2nd <- HNL.Q.2nd[HNL.Q.2nd[,6] > sort(HNL.Q.2nd[,6], decreasing=TRUE)[topNo+1], ]
      HNL.Q <- rbind(HNL.Q.1st, HNL.Q.2nd)
    }
    
    # Database search with memory management
    score_results <- search_database_optimized(HNL.Q, database, bad.No, premass.Q, mass.Q)
    
    if(nrow(score_results) > 0){
      # Save results immediately to free memory
      filename <- paste0('output_premass', premass.Q, '_rt', rt.Q, '.csv')
      setwd(paste0(data.path, '/output'))
      write.csv(score_results, file = filename, row.names = FALSE)
      setwd(data.path)
      
      results[[length(results) + 1]] <- list(
        spectrum_idx = spectrum_idx,
        filename = filename,
        n_hits = nrow(score_results)
      )
    }
    
    # Periodic garbage collection
    if(i %% gc.interval == 0){
      gc()
      monitor_memory(paste("spectrum", spectrum_idx))
    }
  }
  
  return(results)
}

# Optimized database search function
search_database_optimized <- function(HNL.Q, database, bad.No, premass.Q, mass.Q) {
  # Pre-allocate score matrix with reasonable size
  max_matches <- min(length(database), 1000)
  score.matrix <- matrix(NA, nrow = max_matches, ncol = 9)
  colnames(score.matrix) <- c('CSS intensity score','matched HNL No.','std HNL No.',
                             'name','formula','database No.','SMILES','InChIKey','ion mode')

  h <- 1

  # Read biotransformation data once
  setwd(paste0(data.path, '/biotrans'))
  if(!exists("bp_global")) {
    bp_global <<- read.csv('biotrans-plus.csv', stringsAsFactors = FALSE)
    bm_global <<- read.csv('biotrans-minus.csv', stringsAsFactors = FALSE)
    brlist_global <<- read.csv('biotrans mass change list.csv', stringsAsFactors = FALSE)
    bruniquemass_global <<- unique(brlist_global[,15])
  }

  for(l in 1:length(database)){
    if(h > max_matches) break  # Prevent memory overflow
    if(is.element(l, bad.No)) next

    # Quick mass filter
    if(!is.null(database[[l]]$PrecursorMZ)){
      if(abs(database[[l]]$PrecursorMZ - premass.Q) > mass.range) next
    }

    # Extract and clean formula
    formula.L <- database[[l]]$Formula
    if(grepl('\\[', formula.L)){
      formula.L <- substring(formula.L, regexpr("\\[", formula.L)+1, regexpr("\\]", formula.L)-1)
    } else if(grepl('\\+|\\-', formula.L)){
      formula.L <- substring(formula.L, 1, nchar(formula.L)-1)
    }

    # Mass calculation and biotransformation check
    tryCatch({
      mass.L <- getMolecule(formula.L, z=0)$exactmass
      mass.list <- mass.L + bruniquemass_global - mass.Q
      if(min(abs(mass.list)) > pre.tol) next
    }, error = function(e) {
      next  # Skip problematic formulas
    })

    # Extract metadata
    name.L <- database[[l]]$Name

    # Ion mode
    ionmode.L <- 'Unknown'
    if(!is.null(database[[l]]$Ion_mode)){
      ionmode.L <- database[[l]]$Ion_mode
    } else if(!is.null(database[[l]]$Precursor_type)){
      str <- substr(database[[l]]$Precursor_type, nchar(database[[l]]$Precursor_type), nchar(database[[l]]$Precursor_type))
      ionmode.L <- ifelse(str == "+", 'P', ifelse(str == "-", 'N', 'Unknown'))
    }

    # SMILES extraction
    smiles.L <- ""
    if(grepl("computed SMILES=", database[[l]]$Comments)){
      a <- substring(database[[l]]$Comments, regexpr("computed SMILES=", database[[l]]$Comments) + 16)
      smiles.L <- strsplit(a, '\"')[[1]][1]
    } else if(grepl("SMILES=", database[[l]]$Comments)){
      a <- substring(database[[l]]$Comments, regexpr("SMILES=", database[[l]]$Comments) + 7)
      smiles.L <- strsplit(a, '\"')[[1]][1]
    }

    # InChIKey
    inchikey.L <- ifelse(!is.null(database[[l]]$InChIKey),
                        database[[l]]$InChIKey,
                        paste0('No InChIKey info:', l))

    # MS2 spectrum processing
    ms2.L <- as.data.frame(database[[l]]$pspectrum)
    if(nrow(ms2.L) == 0) next
    ms2.L[,2] <- 10 * ms2.L[,2] / max(ms2.L[,2])

    # Calculate CSS score
    CSS.list <- CSS.score.optimized(HNL.Q, ms2.L)

    # Store results
    score.matrix[h, ] <- c(as.numeric(CSS.list[1]), as.numeric(CSS.list[2]),
                          as.numeric(CSS.list[3]), name.L, formula.L, l,
                          smiles.L, inchikey.L, ionmode.L)
    h <- h + 1
  }

  # Clean and filter results
  score.matrix <- score.matrix[1:(h-1), , drop = FALSE]
  score.matrix <- as.data.frame(score.matrix, stringsAsFactors = FALSE)

  # Convert numeric columns
  score.matrix[,1] <- as.numeric(score.matrix[,1])
  score.matrix[,2] <- as.numeric(score.matrix[,2])
  score.matrix[,3] <- as.numeric(score.matrix[,3])
  score.matrix[,6] <- as.numeric(score.matrix[,6])

  # Filter results
  score.matrix <- score.matrix[complete.cases(score.matrix), ]
  score.matrix <- score.matrix[score.matrix[,1] > 0, ]
  score.matrix <- score.matrix[score.matrix[,3] > 1, ]
  score.matrix <- score.matrix[score.matrix[,2] > 0, ]

  if(nrow(score.matrix) == 0) return(data.frame())

  # Sort by composite score
  composite_score <- 70 * (as.numeric(score.matrix[,2])/topNo) /
                    (0.5 * log10(100 * as.numeric(score.matrix[,3])/topNo)) +
                    5 * as.numeric(score.matrix[,1])
  score.matrix <- score.matrix[order(-composite_score), ]

  # Limit results
  if(nrow(score.matrix) > 500) score.matrix <- score.matrix[1:500, ]

  # Remove duplicates efficiently
  score.matrix <- remove_duplicates_optimized(score.matrix)

  if(nrow(score.matrix) > 100) score.matrix <- score.matrix[1:100, ]

  return(score.matrix)
}

# Optimized duplicate removal
remove_duplicates_optimized <- function(score.matrix) {
  if(nrow(score.matrix) <= 1) return(score.matrix)

  # Create comparison keys
  smiles_key <- tolower(score.matrix[,7])
  name_key <- tolower(score.matrix[,4])
  inchikey_key <- score.matrix[,8]

  # Find duplicates vectorized
  keep_rows <- rep(TRUE, nrow(score.matrix))

  for(i in 1:(nrow(score.matrix)-1)){
    if(!keep_rows[i]) next

    for(j in (i+1):nrow(score.matrix)){
      if(!keep_rows[j]) next

      if(smiles_key[i] == smiles_key[j] ||
         name_key[i] == name_key[j] ||
         inchikey_key[i] == inchikey_key[j]){
        keep_rows[j] <- FALSE
      }
    }
  }

  return(score.matrix[keep_rows, ])
}

# Main execution with memory management
main_execution <- function() {
  cat("Starting McSearch batch processing with memory optimization...\n")
  monitor_memory("start")

  # Load data
  setwd(data.path)
  cat("Loading mzXML data...\n")
  data <- readMzXmlFile(mzXMLfile.name)
  monitor_memory("mzXML loading")

  # Load database and reference files
  setwd(paste0(data.path, '/files'))
  cat("Loading database...\n")

  if(db.name == 'MoNA_HNL library.msp'){
    smiles.db <- read.csv('smiles_db.csv', stringsAsFactors = FALSE)
  }

  database <- read.msp(db.name, only.org = FALSE,
                      org.set = c('C','H','N','O','P','S','F','Cl','Br','I'),
                      noNumbers = NULL)

  bad.spectra <- read.csv(paste0('low quality spectra indices_',
                                substring(db.name, 1, regexpr("\\.", db.name)-1), '.csv'),
                         stringsAsFactors = FALSE)
  bad.No <- bad.spectra[,1]

  monitor_memory("database loading")

  # Create output directory if it doesn't exist
  output_dir <- paste0(data.path, '/output')
  if(!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }

  # Process data in chunks to manage memory
  chunk_size <- 10  # Process 10 spectra at a time
  total_spectra <- length(data)
  all_results <- list()

  cat(sprintf("Processing %d spectra in chunks of %d...\n", total_spectra, chunk_size))

  for(chunk_start in seq(1, total_spectra, chunk_size)) {
    chunk_end <- min(chunk_start + chunk_size - 1, total_spectra)

    cat(sprintf("Processing chunk %d-%d of %d...\n", chunk_start, chunk_end, total_spectra))

    # Extract chunk
    data_chunk <- data[chunk_start:chunk_end]

    # Process chunk
    chunk_results <- process_spectra_chunk(data_chunk, database, bad.No, chunk_start)

    # Store results
    all_results <- c(all_results, chunk_results)

    # Clear chunk from memory
    rm(data_chunk)
    gc()

    monitor_memory(paste("chunk", chunk_start, "-", chunk_end))
  }

  # Clear large objects
  rm(data, database)
  gc()

  # Summary
  cat("\n=== Processing Summary ===\n")
  cat(sprintf("Total spectra processed: %d\n", total_spectra))
  cat(sprintf("Results files created: %d\n", length(all_results)))

  if(length(all_results) > 0) {
    total_hits <- sum(sapply(all_results, function(x) x$n_hits))
    cat(sprintf("Total hits found: %d\n", total_hits))

    cat("\nOutput files:\n")
    for(result in all_results) {
      cat(sprintf("  %s (%d hits)\n", result$filename, result$n_hits))
    }
  }

  monitor_memory("final")
  cat("Processing completed successfully!\n")

  return(all_results)
}

# Element table for molecular formula calculations
element.table <- data.frame(
  element = c('C','H','N','O','P','S','F','Cl','Br','I'),
  number = rep(0, 10),
  stringsAsFactors = FALSE
)

# Execute main function
tryCatch({
  results <- main_execution()
}, error = function(e) {
  cat("Error occurred during processing:\n")
  cat(paste("Error message:", e$message, "\n"))
  cat("Consider reducing chunk_size or checking input data.\n")

  # Emergency memory cleanup
  gc()

  # Print memory usage for debugging
  monitor_memory("error state")
})
