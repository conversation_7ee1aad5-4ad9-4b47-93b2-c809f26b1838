#########################################
# Memory Diagnostic Script for McSearch
# This script helps identify memory bottlenecks
#########################################

# Memory monitoring functions
check_memory_usage <- function() {
  # Get memory info
  mem_info <- gc()
  
  cat("=== Current Memory Usage ===\n")
  cat(sprintf("Used Memory: %.1f MB\n", sum(mem_info[,2])))
  cat(sprintf("Max Memory: %.1f MB\n", sum(mem_info[,6])))
  cat(sprintf("Available Memory: %.1f MB\n", sum(mem_info[,6]) - sum(mem_info[,2])))
  
  # Get system memory if available
  if(.Platform$OS.type == "windows") {
    tryCatch({
      system_mem <- system("wmic OS get TotalVisibleMemorySize /value", intern = TRUE)
      total_kb <- as.numeric(gsub("TotalVisibleMemorySize=", "", system_mem[3]))
      total_mb <- total_kb / 1024
      cat(sprintf("System Total Memory: %.1f MB\n", total_mb))
    }, error = function(e) {
      cat("Could not retrieve system memory info\n")
    })
  }
  
  return(mem_info)
}

# Test data loading to identify bottlenecks
test_data_loading <- function() {
  cat("\n=== Testing Data Loading ===\n")
  
  data.path <- getwd()
  
  # Test mzXML loading
  cat("Testing mzXML file loading...\n")
  start_mem <- check_memory_usage()
  
  tryCatch({
    library('readMzXmlData')
    mzXMLfile.name <- 'test/test.mzXML'
    
    if(file.exists(mzXMLfile.name)) {
      data <- readMzXmlFile(mzXMLfile.name)
      cat(sprintf("Successfully loaded %d spectra\n", length(data)))
      
      after_mem <- check_memory_usage()
      mem_increase <- sum(after_mem[,2]) - sum(start_mem[,2])
      cat(sprintf("Memory increase from mzXML: %.1f MB\n", mem_increase))
      
      # Test spectrum processing
      cat("Testing spectrum processing...\n")
      ms2_count <- 0
      for(i in 1:min(10, length(data))) {
        if(data[[i]]$metaData$msLevel == 2) {
          ms2_count <- ms2_count + 1
          spectrum_size <- length(data[[i]]$spectrum$mass)
          cat(sprintf("Spectrum %d: %d peaks\n", i, spectrum_size))
        }
      }
      cat(sprintf("Found %d MS2 spectra in first 10\n", ms2_count))
      
      rm(data)
      gc()
      
    } else {
      cat("mzXML file not found. Please check the path.\n")
    }
    
  }, error = function(e) {
    cat(sprintf("Error loading mzXML: %s\n", e$message))
  })
  
  # Test database loading
  cat("\nTesting database loading...\n")
  start_mem <- check_memory_usage()
  
  tryCatch({
    library('metaMS')
    setwd(paste0(data.path, '/files'))
    
    db.name <- 'Fiehn HILIC_HNL library.msp'
    if(file.exists(db.name)) {
      database <- read.msp(db.name, only.org = FALSE,
                          org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                          noNumbers = NULL)
      
      cat(sprintf("Successfully loaded database with %d entries\n", length(database)))
      
      after_mem <- check_memory_usage()
      mem_increase <- sum(after_mem[,2]) - sum(start_mem[,2])
      cat(sprintf("Memory increase from database: %.1f MB\n", mem_increase))
      
      # Test database entry sizes
      entry_sizes <- sapply(database[1:min(10, length(database))], function(x) {
        if(!is.null(x$pspectrum)) {
          return(nrow(x$pspectrum))
        } else {
          return(0)
        }
      })
      
      cat(sprintf("Average spectrum size in database: %.1f peaks\n", mean(entry_sizes)))
      cat(sprintf("Max spectrum size in database: %d peaks\n", max(entry_sizes)))
      
      rm(database)
      gc()
      
    } else {
      cat("Database file not found. Please check the path.\n")
    }
    
  }, error = function(e) {
    cat(sprintf("Error loading database: %s\n", e$message))
  })
  
  setwd(data.path)
}

# Test HNL matrix generation (the most memory-intensive part)
test_hnl_generation <- function() {
  cat("\n=== Testing HNL Matrix Generation ===\n")
  
  # Simulate different spectrum sizes
  spectrum_sizes <- c(10, 20, 30, 50, 100)
  
  for(size in spectrum_sizes) {
    cat(sprintf("\nTesting with %d peaks...\n", size))
    start_mem <- check_memory_usage()
    
    # Create mock spectrum
    ms2.Q <- data.frame(
      mz = sort(runif(size, 50, 500)),
      intensity = runif(size, 1, 100)
    )
    
    # Calculate expected HNL combinations
    expected_combinations <- size * (size - 1) / 2
    cat(sprintf("Expected HNL combinations: %d\n", expected_combinations))
    
    # Test memory usage
    tryCatch({
      # Simulate HNL matrix creation
      HNL.Q <- data.frame(matrix(ncol=6, nrow=expected_combinations))
      colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
      
      h <- 1
      for(m in 1:(nrow(ms2.Q)-1)){
        for(n in (m+1):nrow(ms2.Q)){
          HNL.Q[h,1] <- ms2.Q[n,1] - ms2.Q[m,1]
          HNL.Q[h,2] <- ms2.Q[n,1]
          HNL.Q[h,3] <- ms2.Q[m,1]
          HNL.Q[h,4] <- ms2.Q[n,2]
          HNL.Q[h,5] <- ms2.Q[m,2]
          HNL.Q[h,6] <- 0.5*(ms2.Q[n,2] + ms2.Q[m,2])
          h <- h + 1
        }
      }
      
      after_mem <- check_memory_usage()
      mem_increase <- sum(after_mem[,2]) - sum(start_mem[,2])
      cat(sprintf("Memory increase: %.1f MB\n", mem_increase))
      cat(sprintf("Memory per combination: %.3f KB\n", mem_increase * 1024 / expected_combinations))
      
      rm(HNL.Q, ms2.Q)
      gc()
      
    }, error = function(e) {
      cat(sprintf("Error in HNL generation: %s\n", e$message))
    })
  }
}

# Recommend memory settings
recommend_settings <- function() {
  cat("\n=== Memory Optimization Recommendations ===\n")
  
  mem_info <- check_memory_usage()
  available_mb <- sum(mem_info[,6]) - sum(mem_info[,2])
  
  if(available_mb < 1000) {
    cat("WARNING: Low available memory (< 1GB)\n")
    cat("Recommendations:\n")
    cat("1. Close other applications\n")
    cat("2. Use smaller chunk sizes (chunk_size = 5)\n")
    cat("3. Reduce topNo parameter (topNo = 50)\n")
    cat("4. Process fewer database entries at once\n")
  } else if(available_mb < 2000) {
    cat("CAUTION: Moderate available memory (< 2GB)\n")
    cat("Recommendations:\n")
    cat("1. Use moderate chunk sizes (chunk_size = 10)\n")
    cat("2. Monitor memory usage during processing\n")
    cat("3. Consider using the optimized script\n")
  } else {
    cat("GOOD: Sufficient available memory (> 2GB)\n")
    cat("You should be able to run the original script, but the optimized version is still recommended.\n")
  }
  
  cat(sprintf("\nSuggested R memory limit: %.0f MB\n", available_mb * 0.8))
  cat("To set memory limit in R, use: memory.limit(size = XXXX)\n")
}

# Main diagnostic function
run_diagnostics <- function() {
  cat("McSearch Memory Diagnostic Tool\n")
  cat("==============================\n")
  
  # Check current memory
  check_memory_usage()
  
  # Test data loading
  test_data_loading()
  
  # Test HNL generation
  test_hnl_generation()
  
  # Provide recommendations
  recommend_settings()
  
  cat("\n=== Diagnostic Complete ===\n")
  cat("If you're experiencing memory issues, try:\n")
  cat("1. Use the optimized script (McSearch_batch_mzXML_optimized.r)\n")
  cat("2. Reduce chunk_size parameter\n")
  cat("3. Increase R memory limit\n")
  cat("4. Process smaller datasets\n")
}

# Run diagnostics
run_diagnostics()
