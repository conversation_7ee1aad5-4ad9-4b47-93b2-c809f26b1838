# McSearch

McSearch is a program for automated metabolite annotation for LC-MS/MS data. 

McSearch utilizes CSS algorithm, HNL library and biotransformation database to achievie metabolite annotation using the structural analogs of query compounds.

For McSearch, we provide both single search mode and batch search mode. The input for single search mode is a csv file (see input_single_search.csv as a template). For batch mode, we currently accept raw data of MGF or mzXML format as input.

We also provide a script for HNL library construction. Users can generate HNL libraries from their own tandem mass spectral databases (in msp format).

Currently, McSearch is accessible either by R or on website. (our website: cloudmetabolomics.ca/mcsearch)

For more details, please refer to the McSearch user protocols.

October 2020

HuanLab

# Citation
<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ‘Retrieving and Utilizing Hypothetical Neutral Losses from Tandem Mass Spectra for Spectral Similarity Analysis and Unknown Metabolite Annotation’, **Analytical Chemistry** 2020, 92, 21, 14476–14483.
https://pubs.acs.org/doi/10.1021/acs.analchem.0c02521

