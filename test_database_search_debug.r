# Debug database search for spectrum 49
library('readMzXmlData')
library('metaMS')
library("CHNOSZ")
library('Rdisop')

# Parameters
data.path <- getwd()
mzXMLfile.name <- 'test/test.mzXML'
pre.tol <- 0.01
mz.tol <- 0.01
HNL.threshold <- 36
ion.mode <- 'p'

# Load data
data <- readMzXmlFile(mzXMLfile.name)
spectrum <- data[[49]]

# Load database
setwd(paste0(data.path, '/files'))
database <- read.msp('Fiehn HILIC_HNL library.msp', only.org = FALSE,
                    org.set = c('C','H','N','O','P','S','F','Cl','Br','I'), 
                    noNumbers = NULL)

bad.spectra <- read.csv('low quality spectra indices_Fiehn HILIC_HNL library.csv',
                       stringsAsFactors = FALSE)
bad.No <- bad.spectra[,1]

setwd(data.path)

cat("=== Database Search Debug for Spectrum 49 ===\n")
cat("Database size:", length(database), "\n")
cat("Bad spectra count:", length(bad.No), "\n")

# Process spectrum 49
premass.Q <- spectrum$metaData$precursorMz
ms2.Q <- as.data.frame(cbind(spectrum$spectrum$mass, spectrum$spectrum$intensity))

# Calculate neutral mass
mass.Q <- premass.Q - 1.007276

cat("Precursor m/z:", premass.Q, "\n")
cat("Neutral mass:", mass.Q, "\n")

# Load biotransformation data
setwd(paste0(data.path, '/biotrans'))
bp <- read.csv('biotrans-plus.csv', stringsAsFactors = FALSE)
bm <- read.csv('biotrans-minus.csv', stringsAsFactors = FALSE)
brlist <- read.csv('biotrans mass change list.csv', stringsAsFactors = FALSE)
bruniquemass <- unique(brlist[,15])

cat("Biotransformation masses loaded:", length(bruniquemass), "\n")

setwd(data.path)

# Test database search on first few entries
cat("\n=== Testing Database Entries ===\n")
test_entries <- min(10, length(database))

for(l in 1:test_entries) {
  cat(sprintf("\n--- Testing database entry %d ---\n", l))
  
  if(is.element(l, bad.No)) {
    cat("Skipping bad spectrum\n")
    next
  }
  
  # Quick mass filter
  if(!is.null(database[[l]]$PrecursorMZ) && !is.na(database[[l]]$PrecursorMZ)){
    mass_diff <- abs(database[[l]]$PrecursorMZ - premass.Q)
    cat("Precursor m/z:", database[[l]]$PrecursorMZ, ", diff:", mass_diff, "\n")
    if(mass_diff > 200) {
      cat("Skipping due to mass filter\n")
      next
    }
  } else {
    cat("No precursor m/z info\n")
  }
  
  # Extract and clean formula
  formula.L <- database[[l]]$Formula
  cat("Original formula:", formula.L, "\n")
  
  if(is.null(formula.L) || is.na(formula.L) || formula.L == "") {
    cat("Skipping due to missing formula\n")
    next
  }
  
  # Clean formula
  if(grepl('\\[', formula.L)){
    formula.L <- substring(formula.L, regexpr("\\[", formula.L)+1, regexpr("\\]", formula.L)-1)
  } else if(grepl('\\+|\\-', formula.L)){
    formula.L <- substring(formula.L, 1, nchar(formula.L)-1)
  }
  
  cat("Cleaned formula:", formula.L, "\n")
  
  # Mass calculation and biotransformation check
  tryCatch({
    mass.L <- getMolecule(formula.L, z=0)$exactmass
    cat("Library mass:", mass.L, "\n")
    
    if(is.na(mass.L) || is.na(mass.Q)) {
      cat("Skipping due to NA mass\n")
      next
    }
    
    mass.list <- mass.L + bruniquemass - mass.Q
    cat("Mass differences range:", min(mass.list, na.rm=TRUE), "to", max(mass.list, na.rm=TRUE), "\n")
    
    if(any(is.na(mass.list))) {
      cat("WARNING: NA values in mass.list\n")
      mass.list <- mass.list[!is.na(mass.list)]
    }
    
    if(length(mass.list) == 0 || min(abs(mass.list)) > pre.tol) {
      cat("Skipping due to mass tolerance\n")
      next
    }
    
    cat("Passed mass filter\n")
    
    # Extract name
    name.L <- database[[l]]$Name
    cat("Name:", name.L, "\n")
    
    # MS2 spectrum processing
    ms2.L <- as.data.frame(database[[l]]$pspectrum)
    if(nrow(ms2.L) == 0) {
      cat("Skipping due to empty spectrum\n")
      next
    }
    
    cat("Library spectrum peaks:", nrow(ms2.L), "\n")
    
    # Test intensity normalization
    max_int <- max(ms2.L[,2])
    if(max_int > 0) {
      ms2.L[,2] <- 10 * ms2.L[,2] / max_int
      cat("Library spectrum normalized\n")
    } else {
      cat("WARNING: Zero max intensity in library spectrum\n")
    }
    
    cat("Entry", l, "processed successfully\n")
    
  }, error = function(e) {
    cat("ERROR in entry", l, ":", e$message, "\n")
  })
}

cat("\n=== Database Search Debug Complete ===\n")
