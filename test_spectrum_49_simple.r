# Simple test script to debug spectrum 49 issue
library('readMzXmlData')

# Load data
data.path <- getwd()
mzXMLfile.name <- 'test/test.mzXML'
data <- readMzXmlFile(mzXMLfile.name)

# Check spectrum 49 specifically
spectrum_idx <- 49
spectrum <- data[[spectrum_idx]]

cat("=== Spectrum 49 Debug Info ===\n")
cat("MS Level:", spectrum$metaData$msLevel, "\n")
cat("Precursor m/z:", spectrum$metaData$precursorMz, "\n")
cat("Number of peaks:", length(spectrum$spectrum$mass), "\n")

if(spectrum$metaData$msLevel == 2 && length(spectrum$spectrum$mass) > 0) {
  # Extract basic info
  premass.Q <- spectrum$metaData$precursorMz
  ms2.Q <- as.data.frame(cbind(spectrum$spectrum$mass, spectrum$spectrum$intensity))
  
  cat("Precursor m/z value:", premass.Q, "\n")
  cat("Is precursor NA?", is.na(premass.Q), "\n")
  cat("Is precursor finite?", is.finite(premass.Q), "\n")
  
  cat("MS2 data summary:\n")
  cat("  Rows:", nrow(ms2.Q), "\n")
  cat("  m/z range:", min(ms2.Q[,1], na.rm=TRUE), "-", max(ms2.Q[,1], na.rm=TRUE), "\n")
  cat("  Intensity range:", min(ms2.Q[,2], na.rm=TRUE), "-", max(ms2.Q[,2], na.rm=TRUE), "\n")
  cat("  NA values in m/z:", sum(is.na(ms2.Q[,1])), "\n")
  cat("  NA values in intensity:", sum(is.na(ms2.Q[,2])), "\n")
  cat("  Infinite values in m/z:", sum(is.infinite(ms2.Q[,1])), "\n")
  cat("  Infinite values in intensity:", sum(is.infinite(ms2.Q[,2])), "\n")
  
  # Test the problematic conditions step by step
  cat("\n=== Testing conditions that might cause NA in logical operations ===\n")
  
  # Test ion mode calculation
  ion.mode <- 'p'
  if(ion.mode %in% c('P','p')){ 
    mass.Q <- premass.Q - 1.007276
    cat("Calculated mass.Q:", mass.Q, "\n")
    cat("Is mass.Q NA?", is.na(mass.Q), "\n")
  }
  
  # Test data cleaning steps
  cat("\n=== Testing data cleaning steps ===\n")
  
  # Step 1: Remove invalid values
  valid_rows <- !is.na(ms2.Q[,1]) & !is.na(ms2.Q[,2]) & 
                is.finite(ms2.Q[,1]) & is.finite(ms2.Q[,2]) &
                ms2.Q[,1] > 0 & ms2.Q[,2] > 0
  
  cat("Valid rows check:\n")
  cat("  Total rows:", length(valid_rows), "\n")
  cat("  Valid rows:", sum(valid_rows, na.rm=TRUE), "\n")
  cat("  Any NA in valid_rows?", any(is.na(valid_rows)), "\n")
  
  if(any(is.na(valid_rows))) {
    cat("  NA positions in valid_rows:", which(is.na(valid_rows)), "\n")
    
    # Check each condition separately
    cat("  !is.na(ms2.Q[,1]) has NA?", any(is.na(!is.na(ms2.Q[,1]))), "\n")
    cat("  !is.na(ms2.Q[,2]) has NA?", any(is.na(!is.na(ms2.Q[,2]))), "\n")
    cat("  is.finite(ms2.Q[,1]) has NA?", any(is.na(is.finite(ms2.Q[,1]))), "\n")
    cat("  is.finite(ms2.Q[,2]) has NA?", any(is.na(is.finite(ms2.Q[,2]))), "\n")
    cat("  ms2.Q[,1] > 0 has NA?", any(is.na(ms2.Q[,1] > 0)), "\n")
    cat("  ms2.Q[,2] > 0 has NA?", any(is.na(ms2.Q[,2] > 0)), "\n")
  }
  
  # Try to use the valid_rows for subsetting
  if(any(valid_rows, na.rm=TRUE)) {
    # Replace NA values in valid_rows with FALSE
    valid_rows[is.na(valid_rows)] <- FALSE
    
    ms2.Q_clean <- ms2.Q[valid_rows, ]
    cat("After cleaning - rows:", nrow(ms2.Q_clean), "\n")
    
    if(nrow(ms2.Q_clean) > 0) {
      # Test normalization
      max_intensity <- max(ms2.Q_clean[,2])
      cat("Max intensity:", max_intensity, "\n")
      
      if(max_intensity > 0) {
        ms2.Q_clean[,2] <- 100 * ms2.Q_clean[,2] / max_intensity
        cat("After normalization - intensity range:", 
            min(ms2.Q_clean[,2]), "-", max(ms2.Q_clean[,2]), "\n")
        
        # Test other filters
        intensity_filter <- ms2.Q_clean[,2] >= 1
        cat("Intensity filter - any NA?", any(is.na(intensity_filter)), "\n")
        cat("Intensity filter - TRUE count:", sum(intensity_filter, na.rm=TRUE), "\n")
        
        if(!any(is.na(intensity_filter))) {
          ms2.Q_clean <- ms2.Q_clean[intensity_filter, ]
          cat("After intensity filter - rows:", nrow(ms2.Q_clean), "\n")
          
          if(nrow(ms2.Q_clean) > 0) {
            # Test m/z filter
            mz_filter <- ms2.Q_clean[,1] <= (premass.Q + 0.01)
            cat("m/z filter - any NA?", any(is.na(mz_filter)), "\n")
            cat("m/z filter - TRUE count:", sum(mz_filter, na.rm=TRUE), "\n")
            
            if(!any(is.na(mz_filter))) {
              ms2.Q_clean <- ms2.Q_clean[mz_filter, ]
              cat("After m/z filter - rows:", nrow(ms2.Q_clean), "\n")
              cat("SUCCESS: All filters passed without NA issues\n")
            } else {
              cat("PROBLEM: m/z filter contains NA values\n")
            }
          }
        } else {
          cat("PROBLEM: Intensity filter contains NA values\n")
        }
      }
    }
  } else {
    cat("PROBLEM: No valid rows found\n")
  }
  
} else {
  cat("Spectrum 49 is not MS2 or has no peaks\n")
}

cat("\n=== Debug Complete ===\n")
