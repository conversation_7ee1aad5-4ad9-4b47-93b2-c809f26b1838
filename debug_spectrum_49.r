#########################################
# Debug script for spectrum 49 issue
# This script helps identify the specific problem
#########################################

# Load required libraries
library('readMzXmlData')
library('clue')
library('metaMS')
library("CHNOSZ")
library("ChemmineR") 
library('ChemmineOB')
library("fmcsR")
library('stringr')
library('dplyr')
library('Rdisop')

# Parameters
data.path <- getwd()
mzXMLfile.name <- 'test/test.mzXML'
mz.tol <- 0.01
topNo <- 100
HNL.threshold <- 36
ion.mode <- 'p'

# Debug function for specific spectrum
debug_spectrum <- function(spectrum_index) {
  cat(sprintf("Debugging spectrum %d...\n", spectrum_index))
  
  # Load data
  setwd(data.path)
  data <- readMzXmlFile(mzXMLfile.name)
  
  if(spectrum_index > length(data)) {
    cat("Spectrum index out of range\n")
    return(NULL)
  }
  
  spectrum <- data[[spectrum_index]]
  
  # Check if it's MS2
  if(spectrum$metaData$msLevel != 2) {
    cat("Not an MS2 spectrum\n")
    return(NULL)
  }
  
  if(length(spectrum$spectrum$mass) == 0) {
    cat("Empty spectrum\n")
    return(NULL)
  }
  
  cat(sprintf("Original spectrum has %d peaks\n", length(spectrum$spectrum$mass)))
  
  # Extract spectrum data
  premass.Q <- spectrum$metaData$precursorMz
  ms2.Q <- as.data.frame(cbind(spectrum$spectrum$mass, spectrum$spectrum$intensity))
  
  cat(sprintf("Precursor m/z: %.4f\n", premass.Q))
  cat(sprintf("Intensity range: %.2f - %.2f\n", min(ms2.Q[,2]), max(ms2.Q[,2])))
  
  # Check for problematic values
  cat("Checking for problematic values...\n")
  cat(sprintf("NA values in m/z: %d\n", sum(is.na(ms2.Q[,1]))))
  cat(sprintf("NA values in intensity: %d\n", sum(is.na(ms2.Q[,2]))))
  cat(sprintf("Infinite values in m/z: %d\n", sum(is.infinite(ms2.Q[,1]))))
  cat(sprintf("Infinite values in intensity: %d\n", sum(is.infinite(ms2.Q[,2]))))
  cat(sprintf("Zero or negative m/z: %d\n", sum(ms2.Q[,1] <= 0)))
  cat(sprintf("Negative intensities: %d\n", sum(ms2.Q[,2] < 0)))
  
  # Clean data step by step
  cat("\nCleaning data...\n")
  original_rows <- nrow(ms2.Q)
  
  # Remove invalid values
  ms2.Q <- ms2.Q[is.finite(ms2.Q[,1]) & is.finite(ms2.Q[,2]), ]
  cat(sprintf("After removing infinite values: %d peaks (removed %d)\n", 
              nrow(ms2.Q), original_rows - nrow(ms2.Q)))
  
  ms2.Q <- ms2.Q[ms2.Q[,1] > 0 & ms2.Q[,2] > 0, ]
  cat(sprintf("After removing zero/negative values: %d peaks\n", nrow(ms2.Q)))
  
  if(nrow(ms2.Q) == 0) {
    cat("No valid peaks remaining\n")
    return(NULL)
  }
  
  # Add ion mode specific pseudo peak
  if(ion.mode %in% c('P','p')){
    ms2.Q <- rbind(c(1.007276, 0), ms2.Q)
  } else {
    ms2.Q <- rbind(c(-1.007276, 0), ms2.Q)
  }
  
  # Normalize intensities
  max_intensity <- max(ms2.Q[,2])
  if(max_intensity > 0) {
    ms2.Q[,2] <- 100 * ms2.Q[,2] / max_intensity
  }
  
  # Apply filters
  ms2.Q <- ms2.Q[ms2.Q[,2] >= 1, ]
  ms2.Q <- ms2.Q[ms2.Q[,1] <= (premass.Q + mz.tol), ]
  
  cat(sprintf("After intensity and m/z filters: %d peaks\n", nrow(ms2.Q)))
  
  if(nrow(ms2.Q) > 30){
    intensity_threshold <- sort(ms2.Q[,2], decreasing=TRUE)[31]
    ms2.Q <- ms2.Q[ms2.Q[,2] > intensity_threshold, ]
    cat(sprintf("After top 30 filter: %d peaks\n", nrow(ms2.Q)))
  }
  
  # Square root transformation
  ms2.Q[,2] <- sqrt(pmax(ms2.Q[,2], 0))
  
  # Add precursor if missing
  if(min(abs(ms2.Q[,1] - premass.Q)) > mz.tol){
    ms2.Q <- rbind(ms2.Q, c(premass.Q, 0))
    cat("Added precursor peak\n")
  }
  
  cat(sprintf("Final spectrum: %d peaks\n", nrow(ms2.Q)))
  
  # Test HNL generation
  cat("\nTesting HNL generation...\n")
  n_peaks <- nrow(ms2.Q)
  expected_combinations <- n_peaks * (n_peaks - 1) / 2
  cat(sprintf("Expected HNL combinations: %d\n", expected_combinations))
  
  # Generate HNL matrix
  HNL.Q <- data.frame(matrix(ncol=6, nrow=0))
  colnames(HNL.Q) <- c('HNL','mz.a','mz.b','int.a','int.b','HNL.int')
  
  valid_hnl_count <- 0
  for(m in 1:(n_peaks-1)){
    for(n in (m+1):n_peaks){
      hnl_value <- ms2.Q[n,1] - ms2.Q[m,1]
      
      if(!is.na(hnl_value) && !is.infinite(hnl_value) && hnl_value >= HNL.threshold){
        int_a <- ms2.Q[n,2]
        int_b <- ms2.Q[m,2]
        
        if(is.na(int_a) || is.infinite(int_a)) int_a <- 0
        if(is.na(int_b) || is.infinite(int_b)) int_b <- 0
        
        hnl_int <- ifelse(m==1, int_a, 0.5*(int_a + int_b))
        if(is.na(hnl_int) || is.infinite(hnl_int)) hnl_int <- 0
        
        HNL.Q <- rbind(HNL.Q, c(hnl_value, ms2.Q[n,1], ms2.Q[m,1], int_a, int_b, hnl_int))
        valid_hnl_count <- valid_hnl_count + 1
      }
    }
  }
  
  cat(sprintf("Valid HNL pairs generated: %d\n", valid_hnl_count))
  
  if(nrow(HNL.Q) > 0) {
    cat("HNL matrix summary:\n")
    cat(sprintf("HNL range: %.3f - %.3f\n", min(HNL.Q[,1]), max(HNL.Q[,1])))
    cat(sprintf("Intensity range: %.3f - %.3f\n", min(HNL.Q[,6]), max(HNL.Q[,6])))
    
    # Check for problematic values in HNL matrix
    cat(sprintf("NA values in HNL matrix: %d\n", sum(is.na(HNL.Q))))
    cat(sprintf("Infinite values in HNL matrix: %d\n", sum(sapply(HNL.Q, is.infinite))))
    
    return(list(
      spectrum_index = spectrum_index,
      premass = premass.Q,
      n_peaks = nrow(ms2.Q),
      n_hnl = nrow(HNL.Q),
      ms2_data = ms2.Q,
      hnl_data = HNL.Q
    ))
  } else {
    cat("No valid HNL pairs found\n")
    return(NULL)
  }
}

# Debug spectrum 49
cat("=== Debugging Spectrum 49 ===\n")
result <- debug_spectrum(49)

if(!is.null(result)) {
  cat("\n=== Testing CSS Score Calculation ===\n")
  
  # Create a simple mock library spectrum for testing
  mock_library <- data.frame(
    mz = c(50, 100, 150, 200),
    intensity = c(10, 20, 15, 5)
  )
  
  # Test CSS score calculation
  tryCatch({
    # Simplified CSS score test
    HNL.q <- result$hnl_data
    ms2.l <- mock_library
    
    cat("Testing CSS score calculation...\n")
    cat(sprintf("Query HNL pairs: %d\n", nrow(HNL.q)))
    cat(sprintf("Library peaks: %d\n", nrow(ms2.l)))
    
    # Test the problematic part - matrix creation for Hungarian algorithm
    HNL.alignment <- data.frame(matrix(ncol=4, nrow=0))
    colnames(HNL.alignment) <- c('HNL.q','int.q','HNL.l','int.l')
    
    for(m in 1:min(10, nrow(HNL.q))){  # Test only first 10 to avoid too much output
      mz.diff <- abs(HNL.q[m,1] - ms2.l[,1])
      matches <- which(mz.diff <= mz.tol)
      
      if(length(matches) > 0){
        for(match_idx in matches){
          HNL.alignment <- rbind(HNL.alignment, 
                               c(HNL.q[m,1], HNL.q[m,6], ms2.l[match_idx,1], ms2.l[match_idx,2]))
        }
      }
    }
    
    cat(sprintf("HNL alignments found: %d\n", nrow(HNL.alignment)))
    
    if(nrow(HNL.alignment) > 0) {
      cat("Alignment matrix looks good for CSS calculation\n")
    } else {
      cat("No alignments found - this might be normal with mock data\n")
    }
    
  }, error = function(e) {
    cat(sprintf("Error in CSS calculation: %s\n", e$message))
  })
}

cat("\n=== Debug Complete ===\n")
